from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from datetime import datetime
import sys

class SalesWidget(QWidget):
    """واجهة إدارة المبيعات"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.init_ui()
        self.create_sample_data()

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مع تقليل المساحات الفارغة لاستغلال المساحة للجدول
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش من 10 إلى 2
        main_layout.setSpacing(3)  # تقليل المسافات من 8 إلى 3

        # العنوان الرئيسي مطابق للفواتير
        title_label = QLabel("💰 إدارة المبيعات المتطورة - نظام شامل ومتقدم لإدارة المبيعات مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        main_layout.addWidget(title_label)

        # إطار البحث
        search_frame = QFrame()
        search_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 65px;
                min-height: 60px;
            }
        """)

        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        search_layout.setSpacing(4)  # مسافات متوازنة

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch(1)

        # البحث مطابق للفواتير
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث بالعميل، رقم الفاتورة أو المنتج...")
        # سيتم ربط الأحداث في نهاية init_ui()
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QLineEdit:focus {
                border: 3px solid rgba(139, 92, 246, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.5 rgba(240, 249, 255, 0.9),
                    stop:1 rgba(224, 242, 254, 0.85));
                box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
            }
            QLineEdit:hover {
                border: 3px solid rgba(124, 58, 237, 0.7);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.9),
                    stop:1 rgba(241, 245, 249, 0.85));
            }
        """)

        # فلتر الحالة مطابق للفواتير
        status_label = QLabel("📊 حالة:")
        status_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)
        status_label.setAlignment(Qt.AlignCenter)

        # إنشاء قائمة تصفية مخصصة ومطورة للحالات مطابقة للفواتير
        self.create_custom_status_filter()


        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_edit, 1)
        search_layout.addWidget(status_label)
        search_layout.addWidget(self.status_filter_frame)
        search_layout.addStretch()

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        search_frame.setLayout(top_container)
        main_layout.addWidget(search_frame)

        # جدول المبيعات
        self.sales_table = QTableWidget()
        self.sales_table.setColumnCount(9)
        # عناوين محسنة مع أيقونات متطورة وجذابة مطابقة للفواتير
        headers = [
            "🧾 رقم الفاتورة",
            "👨‍💼 العميل",
            "📅 التاريخ",
            "📦 المنتج",
            "🔢 الكمية",
            "💰 سعر الوحدة",
            "💵 الإجمالي",
            "🎯 الحالة",
            "📋 ملاحظات"
        ]
        self.sales_table.setHorizontalHeaderLabels(headers)

        # إعداد التحديد للسطر كاملاً
        self.sales_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.sales_table.setSelectionMode(QAbstractItemView.SingleSelection)

        # إخفاء شريط التمرير المرئي مع الحفاظ على التمرير بالماوس
        self.sales_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # ضبط إعدادات التمرير للتحكم الدقيق
        try:
            scrollbar = self.sales_table.verticalScrollBar()
            if scrollbar:
                scrollbar.setSingleStep(50)
                scrollbar.setPageStep(200)
        except Exception:
            pass

        # تطبيق تصميم متطور جداً وأنيق للجدول مطابق للمصروفات والإيرادات والفواتير
        self.sales_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }

            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: 600;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9),
                    stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9),
                    stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9),
                    stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-top: 3px solid rgba(255, 255, 255, 0.7) !important;
                border-bottom: 5px solid rgba(255, 255, 255, 0.8) !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15),
                    stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25),
                    stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-top: 2px solid rgba(102, 126, 234, 0.5) !important;
                border-bottom: 4px solid rgba(102, 126, 234, 0.6) !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                font-size: 14px !important;
                transform: translateY(-1px) !important;
            }

            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9),
                    stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }
        """)

        # تطبيق تنسيق العناوين المتطور مطابق للمصروفات والإيرادات والفواتير
        header = self.sales_table.horizontalHeader()
        header.setStyleSheet("")  # إزالة أي تنسيق سابق

        # ألوان مشابهة للعنوان الرئيسي لكن مطورة وهادئة مع خط بولد قوي
        new_header_style = """
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                margin: 0px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-bottom: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-left: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-right: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                vertical-align: middle !important;
                line-height: 31px !important;
                letter-spacing: 1.3px !important;
                position: relative !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.1 #334155, stop:0.2 #475569,
                    stop:0.3 #64748B, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5),
                           inset 0 2px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -1px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-bottom: 5px solid #d4d4aa !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9),
                           1px 1px 2px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.5px !important;
                font-size: 15px !important;
                color: #ffffff !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #1E40AF, stop:0.5 #1D4ED8,
                    stop:0.6 #2563EB, stop:0.7 #5B21B6, stop:0.8 #4C1D95,
                    stop:0.9 #3730A3, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6),
                           0 2px 4px rgba(0, 0, 0, 0.4),
                           inset 0 0 12px rgba(0, 0, 0, 0.4) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                border-top: 2px solid rgba(255, 255, 255, 0.6) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9),
                           0px 0px 1px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.2px !important;
                color: #f0f0f0 !important;
                font-weight: 900 !important;
            }
        """

        # تطبيق التنسيق الجديد
        header.setStyleSheet(new_header_style)

        # تحسين ارتفاع الصفوف مطابق للمصروفات والإيرادات والفواتير
        self.sales_table.verticalHeader().setDefaultSectionSize(45)
        self.sales_table.verticalHeader().setVisible(False)

        # تحسين رأس الجدول بشكل احترافي مع تأثيرات
        header.setFixedHeight(55)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setMinimumSectionSize(120)

        # إضافة العلامة المائية للجدول مطابقة للفواتير
        self.add_watermark_to_sales_table()

        # تعديل عرض الأعمدة
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # رقم الفاتورة
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # العميل
        header.setSectionResizeMode(2, QHeaderView.Fixed)  # التاريخ
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # المنتج
        header.setSectionResizeMode(4, QHeaderView.Fixed)  # الكمية
        header.setSectionResizeMode(5, QHeaderView.Fixed)  # سعر الوحدة
        header.setSectionResizeMode(6, QHeaderView.Fixed)  # الإجمالي
        header.setSectionResizeMode(7, QHeaderView.Fixed)  # الحالة
        header.setSectionResizeMode(8, QHeaderView.Stretch)  # ملاحظات

        # تحديد عرض الأعمدة
        self.sales_table.setColumnWidth(0, 100)  # رقم الفاتورة
        self.sales_table.setColumnWidth(2, 120)  # التاريخ
        self.sales_table.setColumnWidth(4, 80)   # الكمية
        self.sales_table.setColumnWidth(5, 100)  # سعر الوحدة
        self.sales_table.setColumnWidth(6, 100)  # الإجمالي
        self.sales_table.setColumnWidth(7, 80)   # الحالة

        # إضافة معالج التمرير المخصص
        def sales_wheelEvent(event):
            try:
                delta = event.angleDelta().y()
                if abs(delta) < 120:
                    event.accept()
                    return

                scrollbar = self.sales_table.verticalScrollBar()
                if not scrollbar:
                    event.accept()
                    return

                if delta > 0:
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepSub)
                else:
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepAdd)

                event.accept()
            except Exception:
                QTableWidget.wheelEvent(self.sales_table, event)

        self.sales_table.wheelEvent = sales_wheelEvent

        main_layout.addWidget(self.sales_table, 1)

        # إطار الأزرار
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 3px;
                max-height: 65px;
                min-height: 60px;
            }
        """)

        buttons_layout = QHBoxLayout()
        buttons_layout.setContentsMargins(8, 3, 8, 3)  # تقليل المسافات بنسبة بسيطة
        buttons_layout.setSpacing(8)  # تقليل المسافة بين الأزرار قليلاً

        # أزرار العمليات مثل المخزون مع ألوان متنوعة
        self.add_button = QPushButton("➕ إضافة مبيعة")
        self.style_advanced_button(self.add_button, 'emerald', has_menu=True)  # أخضر زمردي مميز مع قائمة
        self.add_button.clicked.connect(self.add_sale)
        self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.edit_button = QPushButton("✏️ تعديل")
        self.style_advanced_button(self.edit_button, 'info')  # أزرق سماوي متطور مطابق للفواتير
        self.edit_button.clicked.connect(self.edit_sale)
        self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.delete_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_button, 'danger')  # أحمر تحذيري
        self.delete_button.clicked.connect(self.delete_sale)
        self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_button, 'modern_teal')  # تصميم حديث ومتطور
        self.refresh_button.clicked.connect(self.refresh_data)
        self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # المجموعة الثانية - العمليات المتقدمة
        self.view_button = QPushButton("👁️ عرض التفاصيل ▼")
        self.style_advanced_button(self.view_button, 'indigo', has_menu=True)  # بنفسجي للتفاصيل
        self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة منسدلة لعرض التفاصيل
        from ui.unified_styles import UnifiedStyles
        view_menu = QMenu(self)
        view_menu.setStyleSheet(UnifiedStyles.get_menu_style('indigo', 'normal'))

        view_details_action = QAction("👁️ عرض التفاصيل", self)
        view_details_action.triggered.connect(self.view_sale_details)
        view_menu.addAction(view_details_action)

        sales_history_action = QAction("📊 تاريخ المبيعات", self)
        sales_history_action.triggered.connect(self.view_sales_history)
        view_menu.addAction(sales_history_action)

        customer_info_action = QAction("👤 معلومات العميل", self)
        customer_info_action.triggered.connect(self.view_customer_info)
        view_menu.addAction(customer_info_action)

        self.view_button.setMenu(view_menu)

        self.export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.export_button, 'info', has_menu=True)  # لون متسق مع نظام الألوان
        self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة منسدلة للتصدير
        export_menu = QMenu(self)
        export_menu.setStyleSheet(UnifiedStyles.get_menu_style('info', 'normal'))

        excel_action = QAction("📊 تصدير إلى Excel", self)
        excel_action.triggered.connect(self.export_to_excel)
        export_menu.addAction(excel_action)

        pdf_action = QAction("📄 تصدير إلى PDF", self)
        pdf_action.triggered.connect(self.export_to_pdf)
        export_menu.addAction(pdf_action)

        csv_action = QAction("📋 تصدير إلى CSV", self)
        csv_action.triggered.connect(self.export_to_csv)
        export_menu.addAction(csv_action)

        json_action = QAction("🔗 تصدير إلى JSON", self)
        json_action.triggered.connect(self.export_to_json)
        export_menu.addAction(json_action)

        self.export_button.setMenu(export_menu)

        self.statistics_button = QPushButton("📊 الإحصائيات")
        self.style_advanced_button(self.statistics_button, 'rose')  # وردي للإحصائيات
        self.statistics_button.clicked.connect(self.show_statistics)
        self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إجمالي المبيعات مطور ليتشابه مع الفواتير
        self.total_sales_label = QLabel("إجمالي المبيعات: 0 | القيمة الإجمالية: 0.00 ج.م")
        self.total_sales_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                padding: 8px 16px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #064e3b,
                    stop:0.1 #047857,
                    stop:0.9 #065f46,
                    stop:1 #10b981);
                border: 5px solid #10b981;
                border-radius: 20px;
                min-height: 34px;
                max-height: 38px;
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                           2px 2px 4px rgba(0, 0, 0, 0.7),
                           1px 1px 2px rgba(0, 0, 0, 0.5);
                box-shadow: 0 8px 20px rgba(16, 185, 129, 0.6),
                           inset 0 3px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(16, 185, 129, 0.6),
                           0 0 40px rgba(255, 255, 255, 0.1);
                letter-spacing: 0.5px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        """)
        self.total_sales_label.setAlignment(Qt.AlignCenter)

        # إضافة الأزرار للتخطيط
        buttons_layout.addWidget(self.add_button)
        buttons_layout.addWidget(self.edit_button)
        buttons_layout.addWidget(self.delete_button)
        buttons_layout.addWidget(self.refresh_button)
        buttons_layout.addWidget(self.view_button)
        buttons_layout.addWidget(self.export_button)
        buttons_layout.addWidget(self.statistics_button)
        buttons_layout.addWidget(self.total_sales_label)

        buttons_frame.setLayout(buttons_layout)
        main_layout.addWidget(buttons_frame)

        # ربط الأحداث في النهاية بعد إنشاء جميع العناصر
        self.connect_events()

        self.setLayout(main_layout)

    def connect_events(self):
        """ربط جميع الأحداث بعد إنشاء جميع العناصر"""
        try:
            # ربط حدث البحث
            self.search_edit.textChanged.connect(self.filter_sales)
            print("✅ تم ربط أحداث المبيعات بنجاح")
        except Exception as e:
            print(f"❌ خطأ في ربط أحداث المبيعات: {str(e)}")

    def filter_sales(self):
        """تصفية المبيعات"""
        search_text = self.search_edit.text().lower()
        status_filter = getattr(self, 'current_status_value', 'all')
        
        for row in range(self.sales_table.rowCount()):
            show_row = True
            
            # فلترة النص
            if search_text:
                row_text = ""
                for col in range(self.sales_table.columnCount()):
                    item = self.sales_table.item(row, col)
                    if item:
                        row_text += item.text().lower() + " "
                
                if search_text not in row_text:
                    show_row = False
            
            # فلترة الحالة
            if status_filter != "all":
                status_item = self.sales_table.item(row, 7)
                if status_item and status_filter not in status_item.text():
                    show_row = False
            
            self.sales_table.setRowHidden(row, not show_row)
        
        # تحديث الملخص بعد التصفية
        self.update_summary()

    def create_sample_data(self):
        """إنشاء بيانات تجريبية للمبيعات"""
        sample_data = [
            ["S001", "أحمد محمد", "2024-01-15", "دهان أبيض", "20", "30.00", "600.00", "مدفوعة", "تم التسليم"],
            ["S002", "فاطمة علي", "2024-01-16", "سيراميك أرضي", "50", "20.00", "1000.00", "معلقة", "في الانتظار"],
            ["S003", "محمد حسن", "2024-01-17", "خشب صنوبر", "10", "120.00", "1200.00", "مدفوعة", "تم التسليم"],
            ["S004", "سارة أحمد", "2024-01-18", "مفاتيح كهربائية", "100", "8.00", "800.00", "مدفوعة", "تم التسليم"],
            ["S005", "عمر خالد", "2024-01-19", "حنفيات مياه", "15", "55.00", "825.00", "معلقة", "قيد المراجعة"]
        ]

        self.sales_table.setRowCount(len(sample_data))

        for row, data in enumerate(sample_data):
            for col, value in enumerate(data):
                # إضافة الأيقونات المناسبة لكل عمود
                if col == 0:  # رقم الفاتورة
                    display_value = f"🧾 {value}"
                    item = QTableWidgetItem(display_value)
                    item.setForeground(QColor("#1e40af"))
                    item.setToolTip(f"🧾 رقم الفاتورة: {value}")
                elif col == 1:  # العميل
                    display_value = f"👤 {value}"
                    item = QTableWidgetItem(display_value)
                    item.setForeground(QColor("#7c3aed"))
                    item.setToolTip(f"👤 العميل: {value}")
                elif col == 2:  # التاريخ
                    display_value = f"📅 {value}"
                    item = QTableWidgetItem(display_value)
                    item.setForeground(QColor("#0891b2"))
                    item.setToolTip(f"📅 تاريخ البيع: {value}")
                elif col == 3:  # المنتج
                    display_value = f"📦 {value}"
                    item = QTableWidgetItem(display_value)
                    item.setForeground(QColor("#059669"))
                    item.setToolTip(f"📦 المنتج: {value}")
                elif col == 4:  # الكمية
                    display_value = f"🔢 {value}"
                    item = QTableWidgetItem(display_value)
                    item.setForeground(QColor("#ea580c"))
                    item.setToolTip(f"🔢 الكمية: {value}")
                elif col == 5:  # سعر الوحدة
                    display_value = f"💰 {value}"
                    item = QTableWidgetItem(display_value)
                    item.setForeground(QColor("#dc2626"))
                    item.setToolTip(f"💰 سعر الوحدة: {value} جنيه")
                elif col == 6:  # الإجمالي
                    display_value = f"💵 {value}"
                    item = QTableWidgetItem(display_value)
                    item.setForeground(QColor("#059669"))
                    item.setToolTip(f"💵 الإجمالي: {value} جنيه")
                elif col == 7:  # الحالة
                    status_icons = {
                        "مدفوعة": "✅",
                        "معلقة": "⏳",
                        "ملغية": "❌"
                    }
                    status_icon = status_icons.get(value, "📋")
                    display_value = f"{status_icon} {value}"
                    item = QTableWidgetItem(display_value)

                    # تلوين الحالة
                    if value == "مدفوعة":
                        item.setForeground(QColor("#059669"))
                        item.setBackground(QColor(200, 255, 200))
                    elif value == "معلقة":
                        item.setForeground(QColor("#d97706"))
                        item.setBackground(QColor(255, 255, 200))
                    elif value == "ملغية":
                        item.setForeground(QColor("#dc2626"))
                        item.setBackground(QColor(255, 200, 200))

                    item.setToolTip(f"{status_icon} حالة البيع: {value}")
                elif col == 8:  # الملاحظات
                    display_value = f"📋 {value}"
                    item = QTableWidgetItem(display_value)
                    item.setForeground(QColor("#6b7280"))
                    item.setToolTip(f"📋 الملاحظات: {value}")
                else:
                    item = QTableWidgetItem(str(value))

                item.setFont(QFont("Segoe UI", 10, QFont.Bold))
                item.setTextAlignment(Qt.AlignCenter)
                self.sales_table.setItem(row, col, item)

        # تحديث الملخص
        self.update_summary()

    def add_sale(self):
        """إضافة مبيعة جديدة"""
        QMessageBox.information(self, "قريباً", "ميزة إضافة مبيعة جديدة ستكون متاحة قريباً!")

    def edit_sale(self):
        """تعديل مبيعة"""
        current_row = self.sales_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "الرجاء اختيار مبيعة للتعديل")
            return

        # الحصول على بيانات السطر المحدد
        invoice_number = self.sales_table.item(current_row, 0).text() if self.sales_table.item(current_row, 0) else ""
        customer = self.sales_table.item(current_row, 1).text() if self.sales_table.item(current_row, 1) else ""
        date = self.sales_table.item(current_row, 2).text() if self.sales_table.item(current_row, 2) else ""
        product = self.sales_table.item(current_row, 3).text() if self.sales_table.item(current_row, 3) else ""
        quantity = self.sales_table.item(current_row, 4).text() if self.sales_table.item(current_row, 4) else ""
        unit_price = self.sales_table.item(current_row, 5).text() if self.sales_table.item(current_row, 5) else ""
        total = self.sales_table.item(current_row, 6).text() if self.sales_table.item(current_row, 6) else ""
        status = self.sales_table.item(current_row, 7).text() if self.sales_table.item(current_row, 7) else ""
        notes = self.sales_table.item(current_row, 8).text() if self.sales_table.item(current_row, 8) else ""

        # إنشاء نافذة التعديل
        dialog = QDialog(self)
        dialog.setWindowTitle(f"✏️ تعديل المبيعة: {invoice_number}")
        dialog.setModal(True)
        dialog.resize(500, 600)

        layout = QVBoxLayout()

        # حقول التعديل
        form_layout = QFormLayout()

        invoice_edit = QLineEdit(invoice_number)
        customer_edit = QLineEdit(customer)
        date_edit = QLineEdit(date)
        product_edit = QLineEdit(product)
        quantity_edit = QLineEdit(quantity)
        unit_price_edit = QLineEdit(unit_price)
        total_edit = QLineEdit(total)
        status_combo = QComboBox()
        status_combo.addItems(["مكتمل", "معلق", "ملغي"])
        status_combo.setCurrentText(status)
        notes_edit = QTextEdit(notes)
        notes_edit.setMaximumHeight(100)

        form_layout.addRow("رقم الفاتورة:", invoice_edit)
        form_layout.addRow("العميل:", customer_edit)
        form_layout.addRow("التاريخ:", date_edit)
        form_layout.addRow("المنتج:", product_edit)
        form_layout.addRow("الكمية:", quantity_edit)
        form_layout.addRow("سعر الوحدة:", unit_price_edit)
        form_layout.addRow("الإجمالي:", total_edit)
        form_layout.addRow("الحالة:", status_combo)
        form_layout.addRow("ملاحظات:", notes_edit)

        layout.addLayout(form_layout)

        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()

        save_button = QPushButton("💾 حفظ التعديلات")
        save_button.clicked.connect(lambda: self.save_sale_changes(
            current_row, invoice_edit.text(), customer_edit.text(), date_edit.text(),
            product_edit.text(), quantity_edit.text(), unit_price_edit.text(),
            total_edit.text(), status_combo.currentText(), notes_edit.toPlainText(), dialog
        ))
        buttons_layout.addWidget(save_button)

        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.clicked.connect(dialog.close)
        buttons_layout.addWidget(cancel_button)

        layout.addLayout(buttons_layout)
        dialog.setLayout(layout)
        dialog.exec_()

    def save_sale_changes(self, row, invoice, customer, date, product, quantity, unit_price, total, status, notes, dialog):
        """حفظ تعديلات المبيعة"""
        try:
            # تحديث البيانات في الجدول
            self.sales_table.setItem(row, 0, QTableWidgetItem(invoice))
            self.sales_table.setItem(row, 1, QTableWidgetItem(customer))
            self.sales_table.setItem(row, 2, QTableWidgetItem(date))
            self.sales_table.setItem(row, 3, QTableWidgetItem(product))
            self.sales_table.setItem(row, 4, QTableWidgetItem(quantity))
            self.sales_table.setItem(row, 5, QTableWidgetItem(unit_price))
            self.sales_table.setItem(row, 6, QTableWidgetItem(total))
            self.sales_table.setItem(row, 7, QTableWidgetItem(status))
            self.sales_table.setItem(row, 8, QTableWidgetItem(notes))

            # تطبيق التنسيق
            for col in range(9):
                item = self.sales_table.item(row, col)
                if item:
                    item.setFont(QFont("Arial", 10, QFont.Bold))
                    item.setForeground(QColor("#000000"))

                    # تلوين حسب الحالة
                    if col == 7:  # عمود الحالة
                        if status == "مكتمل":
                            item.setBackground(QColor("#d1fae5"))
                        elif status == "معلق":
                            item.setBackground(QColor("#fef3c7"))
                        elif status == "ملغي":
                            item.setBackground(QColor("#fee2e2"))

            QMessageBox.information(self, "تم", "تم حفظ التعديلات بنجاح!")
            dialog.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ التعديلات: {str(e)}")

    def delete_sale(self):
        """حذف مبيعة"""
        current_row = self.sales_table.currentRow()
        if current_row >= 0:
            reply = QMessageBox.question(self, "تأكيد الحذف", "هل أنت متأكد من حذف هذه المبيعة؟")
            if reply == QMessageBox.Yes:
                self.sales_table.removeRow(current_row)
                self.update_summary()
                QMessageBox.information(self, "نجح", "تم حذف المبيعة بنجاح!")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مبيعة للحذف")

    def add_watermark_to_sales_table(self):
        """إضافة علامة مائية لجدول المبيعات مطابقة للفواتير"""
        try:
            # إنشاء العلامة المائية مثل الفواتير
            watermark = QLabel("Smart Finish", self.sales_table)
            watermark.setAlignment(Qt.AlignCenter)

            # تنسيق العلامة المائية مكبرة أكثر للتوحيد مع الباقي
            font = QFont("Arial", 150, QFont.Bold)
            watermark.setFont(font)

            # تطبيق تصميم محسن للعلامة المائية - أكثر وضوحاً لكن لا يؤثر على البيانات
            watermark.setStyleSheet("""
                QLabel {
                    color: rgba(71, 85, 105, 0.35);
                    background: transparent;
                    border: none;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.15);
                    font-weight: bold;
                }
            """)

            # إصلاح مشكلة التفاعل - العلامة المائية لا تتداخل مع النقرات
            watermark.setAttribute(Qt.WA_TransparentForMouseEvents, True)
            watermark.setEnabled(False)  # تعطيل التفاعل مع العلامة المائية
            watermark.lower()  # وضع العلامة المائية في الخلف

            # تحسين إضافي للشفافية والوضوح
            watermark.setWindowOpacity(0.6)  # شفافية محسنة

            # تحديد موضع وحجم العلامة المائية مع تحسين الموضع
            def update_watermark_geometry():
                if self.sales_table.isVisible():
                    rect = self.sales_table.rect()
                    # تحسين الموضع ليكون في المنتصف مع مساحة أفضل
                    watermark.setGeometry(rect.x() + 50, rect.y() + 100,
                                        rect.width() - 100, rect.height() - 200)
                    watermark.lower()  # وضع العلامة المائية في الخلف دائماً
                    watermark.setAttribute(Qt.WA_TransparentForMouseEvents, True)

            # ربط تحديث الموضع بتغيير حجم الجدول
            original_resize_event = self.sales_table.resizeEvent
            def custom_resize_event(event):
                original_resize_event(event)
                update_watermark_geometry()
            self.sales_table.resizeEvent = custom_resize_event

            # تحديث أولي للموضع
            update_watermark_geometry()

        except Exception as e:
            print(f"خطأ في إضافة العلامة المائية للمبيعات: {str(e)}")

    def refresh_data(self):
        """تحديث البيانات"""
        try:
            # حفظ السطر المحدد حالياً
            current_row = self.sales_table.currentRow()

            # إعادة تحميل البيانات
            self.create_sample_data()
            self.update_summary()

            # استعادة التحديد إذا كان ممكناً
            if current_row >= 0 and current_row < self.sales_table.rowCount():
                self.sales_table.selectRow(current_row)

            # إظهار رسالة نجاح
            QMessageBox.information(self, "تم التحديث", "🔄 تم تحديث بيانات المبيعات بنجاح!")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحديث البيانات: {str(e)}")

    def update_summary(self):
        """تحديث ملخص المبيعات"""
        try:
            total_count = 0
            total_value = 0.0
            
            for row in range(self.sales_table.rowCount()):
                if not self.sales_table.isRowHidden(row):
                    total_count += 1
                    total_item = self.sales_table.item(row, 6)  # عمود الإجمالي
                    if total_item:
                        total_value += float(total_item.text())
            
            self.total_sales_label.setText(f"إجمالي المبيعات: {total_count} | القيمة الإجمالية: {total_value:.2f} ج.م")
            
        except Exception as e:
            print(f"خطأ في تحديث الملخص: {str(e)}")

    def show_statistics(self):
        """عرض إحصائيات المبيعات"""
        try:
            from database import Sale, get_session
            session = get_session()
            sales = session.query(Sale).all()

            if not sales:
                QMessageBox.information(self, "إحصائيات المبيعات", "لا توجد مبيعات لعرض الإحصائيات")
                return

            # حساب الإحصائيات
            total_sales = len(sales)
            total_amount = sum(sale.total_amount or 0 for sale in sales)
            avg_amount = total_amount / total_sales if total_sales > 0 else 0

            # إحصائيات حسب العميل
            customer_stats = {}
            for sale in sales:
                customer_name = sale.client.name if sale.client else 'غير محدد'
                if customer_name in customer_stats:
                    customer_stats[customer_name]['count'] += 1
                    customer_stats[customer_name]['amount'] += sale.total_amount or 0
                else:
                    customer_stats[customer_name] = {
                        'count': 1,
                        'amount': sale.total_amount or 0
                    }

            # إحصائيات حسب الحالة
            status_stats = {}
            for sale in sales:
                status = sale.status or 'غير محدد'
                if status in status_stats:
                    status_stats[status] += 1
                else:
                    status_stats[status] = 1

            # إنشاء نافذة الإحصائيات
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextBrowser, QPushButton, QHBoxLayout

            dialog = QDialog(self)
            dialog.setWindowTitle("📊 إحصائيات المبيعات")
            dialog.setModal(True)
            dialog.resize(700, 600)

            layout = QVBoxLayout()

            # الإحصائيات العامة
            general_stats = f"""
📊 الإحصائيات العامة:
─────────────────────────────────────────────────────────────────────────────
💰 إجمالي المبيعات: {total_sales}
💵 إجمالي المبلغ: {int(total_amount):,} جنيه
📈 متوسط قيمة المبيعة: {int(avg_amount):,} جنيه

👥 توزيع حسب العميل:
─────────────────────────────────────────────────────────────────────────────
"""

            # أفضل 5 عملاء
            sorted_customers = sorted(customer_stats.items(), key=lambda x: x[1]['amount'], reverse=True)[:5]
            for customer, stats in sorted_customers:
                percentage = (stats['count'] / total_sales) * 100
                general_stats += f"• {customer}: {stats['count']} مبيعة ({percentage:.1f}%) - {int(stats['amount']):,} جنيه\n"

            general_stats += f"""
─────────────────────────────────────────────────────────────────────────────

📋 توزيع حسب الحالة:
─────────────────────────────────────────────────────────────────────────────
"""

            for status, count in status_stats.items():
                percentage = (count / total_sales) * 100
                general_stats += f"• {status}: {count} مبيعة ({percentage:.1f}%)\n"

            # عرض الإحصائيات
            stats_text = QTextBrowser()
            stats_text.setPlainText(general_stats)
            stats_text.setStyleSheet("""
                QTextBrowser {
                    font-family: 'Segoe UI', Arial, sans-serif;
                    font-size: 11px;
                    line-height: 1.4;
                    background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                    padding: 15px;
                }
            """)
            layout.addWidget(stats_text)

            # أزرار الإجراءات
            buttons_layout = QHBoxLayout()

            export_stats_btn = QPushButton("📤 تصدير الإحصائيات")
            export_stats_btn.clicked.connect(lambda: self.export_statistics_report(general_stats))
            buttons_layout.addWidget(export_stats_btn)

            close_button = QPushButton("❌ إغلاق")
            close_button.clicked.connect(dialog.close)
            buttons_layout.addWidget(close_button)

            layout.addLayout(buttons_layout)
            dialog.setLayout(layout)
            dialog.exec_()

            session.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في عرض الإحصائيات: {str(e)}")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة مطابق للفواتير"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر - مطابق للفواتير
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#047857', 'hover_mid': '#059669', 'hover_end': '#10b981', 'hover_bottom': '#34d399',
                    'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#014737', 'pressed_bottom': '#052e16', 'pressed_border': '#064e3b',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.5)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#991b1b', 'hover_mid': '#dc2626', 'hover_end': '#ef4444', 'hover_bottom': '#f87171',
                    'hover_border': '#ef4444', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                    'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                    'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.5)'
                },
                'modern_teal': {
                    'bg_start': '#042f2e', 'bg_mid': '#134e4a', 'bg_end': '#0f766e', 'bg_bottom': '#14b8a6',
                    'hover_start': '#134e4a', 'hover_mid': '#0d9488', 'hover_end': '#14b8a6', 'hover_bottom': '#2dd4bf',
                    'hover_border': '#14b8a6', 'pressed_start': '#042f2e', 'pressed_mid': '#134e4a',
                    'pressed_end': '#0f766e', 'pressed_bottom': '#0d9488', 'pressed_border': '#0f766e',
                    'border': '#14b8a6', 'text': '#ffffff', 'shadow': 'rgba(20, 184, 166, 0.5)'
                },
                'indigo': {
                    'bg_start': '#1e1b4b', 'bg_mid': '#312e81', 'bg_end': '#3730a3', 'bg_bottom': '#6366f1',
                    'hover_start': '#312e81', 'hover_mid': '#4f46e5', 'hover_end': '#6366f1', 'hover_bottom': '#818cf8',
                    'hover_border': '#6366f1', 'pressed_start': '#1e1b4b', 'pressed_mid': '#312e81',
                    'pressed_end': '#3730a3', 'pressed_bottom': '#4f46e5', 'pressed_border': '#3730a3',
                    'border': '#6366f1', 'text': '#ffffff', 'shadow': 'rgba(99, 102, 241, 0.5)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0284c7', 'bg_bottom': '#0ea5e9',
                    'hover_start': '#075985', 'hover_mid': '#0891b2', 'hover_end': '#0ea5e9', 'hover_bottom': '#38bdf8',
                    'hover_border': '#0ea5e9', 'pressed_start': '#082f49', 'pressed_mid': '#0c4a6e',
                    'pressed_end': '#075985', 'pressed_bottom': '#0284c7', 'pressed_border': '#075985',
                    'border': '#0ea5e9', 'text': '#ffffff', 'shadow': 'rgba(14, 165, 233, 0.5)'
                },
                'rose': {
                    'bg_start': '#500724', 'bg_mid': '#831843', 'bg_end': '#9d174d', 'bg_bottom': '#ec4899',
                    'hover_start': '#831843', 'hover_mid': '#be185d', 'hover_end': '#ec4899', 'hover_bottom': '#f472b6',
                    'hover_border': '#ec4899', 'pressed_start': '#500724', 'pressed_mid': '#831843',
                    'pressed_end': '#9d174d', 'pressed_bottom': '#be185d', 'pressed_border': '#9d174d',
                    'border': '#ec4899', 'text': '#ffffff', 'shadow': 'rgba(236, 72, 153, 0.5)'
                }
            }

            # الحصول على ألوان الزر المحدد
            color_scheme = colors.get(button_type, colors['primary'])

            # تطبيق التصميم المتطور والأنيق مع ألوان جديدة وحفظ المقاسات - مطابق للفواتير
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.15 {color_scheme['bg_mid']},
                        stop:0.85 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 4px solid {color_scheme['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-weight: 900;
                    font-size: 13px;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    min-height: 38px;
                    max-height: 38px;
                    min-width: 100px;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                               1px 1px 2px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 6px 15px {color_scheme['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3),
                               0 0 20px {color_scheme['shadow']},
                               0 0 30px rgba(255, 255, 255, 0.1);
                    letter-spacing: 0.3px;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.15 {color_scheme['hover_mid']},
                        stop:0.85 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 4px solid {color_scheme['hover_border']};
                    transform: translateY(-2px);
                    box-shadow: 0 8px 20px {color_scheme['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                               0 0 25px {color_scheme['shadow']},
                               0 0 40px rgba(255, 255, 255, 0.15);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.15 {color_scheme['pressed_mid']},
                        stop:0.85 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 4px solid {color_scheme['pressed_border']};
                    transform: translateY(1px);
                    box-shadow: 0 3px 8px {color_scheme['shadow']},
                               inset 0 1px 0 rgba(255, 255, 255, 0.2),
                               inset 0 -1px 0 rgba(0, 0, 0, 0.5),
                               0 0 15px {color_scheme['shadow']};
                }}
                QPushButton::menu-indicator {{
                    {f"image: none; width: 0px;" if not has_menu else "width: 12px; height: 12px; margin-right: 4px;"}
                }}
            """

            button.setStyleSheet(style)

        except Exception as e:
            print(f"❌ خطأ في تطبيق تصميم الزر المتطور: {str(e)}")

    def export_to_excel(self):
        """تصدير المبيعات إلى Excel (CSV)"""
        self.export_to_csv()

    def export_to_csv(self):
        """تصدير المبيعات إلى CSV"""
        try:
            import csv
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(self, "حفظ ملف CSV", "المبيعات.csv", "CSV Files (*.csv)")
            if file_path:
                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)
                    headers = ['الرقم', 'رقم المبيعة', 'العميل', 'التاريخ', 'المبلغ الإجمالي', 'الحالة']
                    writer.writerow(headers)

                    for row in range(self.sales_table.rowCount()):
                        row_data = []
                        for col in range(self.sales_table.columnCount()):
                            item = self.sales_table.item(row, col)
                            row_data.append(item.text() if item else "")
                        writer.writerow(row_data)
                QMessageBox.information(self, "تم", f"تم تصدير المبيعات بنجاح إلى:\n{file_path}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في التصدير: {str(e)}")

    def export_to_pdf(self):
        """تصدير المبيعات إلى PDF"""
        try:
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QTextDocument
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtCore import QDate

            # حفظ ملف PDF مباشرة
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير المبيعات", "تقرير_المبيعات.pdf", "PDF Files (*.pdf)"
            )

            if file_path:
                # إنشاء محتوى HTML
                html_content = f"""
                <html dir="rtl">
                <head>
                    <meta charset="utf-8">
                    <title>تقرير المبيعات</title>
                    <style>
                        body {{ font-family: Arial, sans-serif; margin: 20px; }}
                        h1 {{ color: #6366f1; text-align: center; }}
                        table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                        th {{ background-color: #f2f2f2; }}
                    </style>
                </head>
                <body>
                    <h1>💰 تقرير المبيعات</h1>
                    <p><strong>تاريخ التقرير:</strong> {QDate.currentDate().toString('yyyy-MM-dd')}</p>

                    <table>
                        <tr>
                            <th>الرقم</th>
                            <th>رقم المبيعة</th>
                            <th>العميل</th>
                            <th>التاريخ</th>
                            <th>المبلغ الإجمالي</th>
                            <th>الحالة</th>
                        </tr>
                """

                total_amount = 0
                for row in range(self.sales_table.rowCount()):
                    sale_id = self.sales_table.item(row, 0).text() if self.sales_table.item(row, 0) else ""
                    sale_number = self.sales_table.item(row, 1).text() if self.sales_table.item(row, 1) else ""
                    customer = self.sales_table.item(row, 2).text() if self.sales_table.item(row, 2) else ""
                    date = self.sales_table.item(row, 3).text() if self.sales_table.item(row, 3) else ""
                    amount_text = self.sales_table.item(row, 4).text() if self.sales_table.item(row, 4) else "0"
                    status = self.sales_table.item(row, 5).text() if self.sales_table.item(row, 5) else ""

                    try:
                        amount = float(amount_text.replace(',', '').replace('جنيه', '').strip())
                        total_amount += amount
                    except:
                        amount = 0

                    html_content += f"""
                        <tr>
                            <td>{sale_id}</td>
                            <td>{sale_number}</td>
                            <td>{customer}</td>
                            <td>{date}</td>
                            <td>{int(amount):,} جنيه</td>
                            <td>{status}</td>
                        </tr>
                    """

                html_content += f"""
                    </table>
                    <h3>إجمالي المبيعات: {int(total_amount):,} جنيه</h3>
                </body>
                </html>
                """

                # إنشاء طابعة PDF
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)
                printer.setPageSize(QPrinter.A4)
                printer.setPageMargins(20, 20, 20, 20, QPrinter.Millimeter)

                # إنشاء مستند وطباعته إلى PDF
                document = QTextDocument()
                document.setHtml(html_content)
                document.print_(printer)

                QMessageBox.information(self, "تم", f"تم تصدير المبيعات إلى PDF بنجاح:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تصدير PDF: {str(e)}")

    def export_to_json(self):
        """تصدير المبيعات إلى JSON"""
        try:
            import json
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف JSON", "المبيعات.json", "JSON Files (*.json)"
            )

            if file_path:
                data = []
                for row in range(self.sales_table.rowCount()):
                    row_data = {
                        'الرقم': self.sales_table.item(row, 0).text() if self.sales_table.item(row, 0) else "",
                        'رقم المبيعة': self.sales_table.item(row, 1).text() if self.sales_table.item(row, 1) else "",
                        'العميل': self.sales_table.item(row, 2).text() if self.sales_table.item(row, 2) else "",
                        'التاريخ': self.sales_table.item(row, 3).text() if self.sales_table.item(row, 3) else "",
                        'المبلغ الإجمالي': self.sales_table.item(row, 4).text() if self.sales_table.item(row, 4) else "",
                        'الحالة': self.sales_table.item(row, 5).text() if self.sales_table.item(row, 5) else ""
                    }
                    data.append(row_data)

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)

                QMessageBox.information(self, "تم", f"تم تصدير المبيعات إلى JSON بنجاح:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تصدير JSON: {str(e)}")

    def export_statistics_report(self, stats_content):
        """تصدير تقرير الإحصائيات"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtCore import QDate

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير الإحصائيات", "إحصائيات_المبيعات.txt", "Text Files (*.txt)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"""
═══════════════════════════════════════════════════════════════════════════════
                            📊 تقرير إحصائيات المبيعات
═══════════════════════════════════════════════════════════════════════════════

📅 تاريخ التقرير: {QDate.currentDate().toString('yyyy-MM-dd')}
⏰ وقت الإنشاء: {QDate.currentDate().toString('hh:mm:ss')}

{stats_content}

═══════════════════════════════════════════════════════════════════════════════
                        تم إنشاء التقرير بواسطة نظام إدارة المبيعات
═══════════════════════════════════════════════════════════════════════════════
""")

                QMessageBox.information(self, "تم", f"تم تصدير تقرير الإحصائيات بنجاح إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تصدير التقرير: {str(e)}")

    def view_sale_details(self):
        """عرض تفاصيل المبيعة المحددة"""
        selected_row = self.sales_table.currentRow()
        if selected_row < 0:
            QMessageBox.warning(self, "تحذير", "الرجاء اختيار مبيعة من القائمة")
            return

        # الحصول على جميع بيانات السطر المحدد
        invoice_number = self.sales_table.item(selected_row, 0).text() if self.sales_table.item(selected_row, 0) else "غير محدد"
        customer = self.sales_table.item(selected_row, 1).text() if self.sales_table.item(selected_row, 1) else "غير محدد"
        date = self.sales_table.item(selected_row, 2).text() if self.sales_table.item(selected_row, 2) else "غير محدد"
        product = self.sales_table.item(selected_row, 3).text() if self.sales_table.item(selected_row, 3) else "غير محدد"
        quantity = self.sales_table.item(selected_row, 4).text() if self.sales_table.item(selected_row, 4) else "0"
        unit_price = self.sales_table.item(selected_row, 5).text() if self.sales_table.item(selected_row, 5) else "0"
        total = self.sales_table.item(selected_row, 6).text() if self.sales_table.item(selected_row, 6) else "0"
        status = self.sales_table.item(selected_row, 7).text() if self.sales_table.item(selected_row, 7) else "غير محدد"
        notes = self.sales_table.item(selected_row, 8).text() if self.sales_table.item(selected_row, 8) else "لا توجد ملاحظات"

        # إنشاء نافذة التفاصيل
        dialog = QDialog(self)
        dialog.setWindowTitle(f"👁️ تفاصيل المبيعة: {invoice_number}")
        dialog.setModal(True)
        dialog.resize(600, 500)

        layout = QVBoxLayout()

        # معلومات المبيعة
        details_text = f"""
💰 تفاصيل المبيعة:
─────────────────────────────────────────────────────────────────────────────
📋 رقم الفاتورة: {invoice_number}
👤 العميل: {customer}
📅 التاريخ: {date}
📦 المنتج: {product}
📊 الكمية: {quantity}
💰 سعر الوحدة: {unit_price}
💵 الإجمالي: {total}
📋 الحالة: {status}
📝 ملاحظات: {notes}
─────────────────────────────────────────────────────────────────────────────

💡 معلومات إضافية:
• تم إنشاء هذا السجل في النظام
• يمكن تعديل البيانات من خلال زر التعديل
• يمكن تصدير هذه البيانات مع التقارير
        """

        # عرض التفاصيل
        details_browser = QTextBrowser()
        details_browser.setPlainText(details_text)
        details_browser.setStyleSheet("""
            QTextBrowser {
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 11px;
                line-height: 1.4;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        layout.addWidget(details_browser)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        edit_button = QPushButton("✏️ تعديل")
        edit_button.clicked.connect(lambda: (dialog.close(), self.edit_sale()))
        buttons_layout.addWidget(edit_button)

        print_button = QPushButton("🖨️ طباعة")
        print_button.clicked.connect(lambda: self.print_sale_details(invoice_number, customer, date, product, quantity, unit_price, total, status, notes))
        buttons_layout.addWidget(print_button)

        close_button = QPushButton("❌ إغلاق")
        close_button.clicked.connect(dialog.close)
        buttons_layout.addWidget(close_button)

        layout.addLayout(buttons_layout)
        dialog.setLayout(layout)
        dialog.exec_()

    def print_sale_details(self, invoice, customer, date, product, quantity, unit_price, total, status, notes):
        """طباعة تفاصيل المبيعة"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtCore import QDate

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تفاصيل المبيعة", f"تفاصيل_المبيعة_{invoice}.txt", "Text Files (*.txt)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"""
═══════════════════════════════════════════════════════════════════════════════
                            💰 تفاصيل المبيعة
═══════════════════════════════════════════════════════════════════════════════

📅 تاريخ الطباعة: {QDate.currentDate().toString('yyyy-MM-dd')}
⏰ وقت الطباعة: {QDate.currentDate().toString('hh:mm:ss')}

📋 معلومات المبيعة:
─────────────────────────────────────────────────────────────────────────────
رقم الفاتورة: {invoice}
العميل: {customer}
التاريخ: {date}
المنتج: {product}
الكمية: {quantity}
سعر الوحدة: {unit_price}
الإجمالي: {total}
الحالة: {status}
ملاحظات: {notes}

═══════════════════════════════════════════════════════════════════════════════
                        تم إنشاء التقرير بواسطة نظام إدارة المبيعات
═══════════════════════════════════════════════════════════════════════════════
""")

                QMessageBox.information(self, "تم", f"تم حفظ تفاصيل المبيعة بنجاح إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ التفاصيل: {str(e)}")

    def view_sales_history(self):
        """عرض تاريخ المبيعات"""
        try:
            # إنشاء نافذة تاريخ المبيعات
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem, QPushButton, QLabel, QDateEdit, QComboBox
            from PyQt5.QtCore import QDate

            dialog = QDialog(self)
            dialog.setWindowTitle("📊 تاريخ المبيعات")
            dialog.setModal(True)
            dialog.resize(900, 600)

            layout = QVBoxLayout()

            # فلاتر التاريخ
            filter_layout = QHBoxLayout()

            filter_layout.addWidget(QLabel("من تاريخ:"))
            from_date = QDateEdit()
            from_date.setDate(QDate.currentDate().addDays(-30))  # آخر 30 يوم
            from_date.setCalendarPopup(True)
            filter_layout.addWidget(from_date)

            filter_layout.addWidget(QLabel("إلى تاريخ:"))
            to_date = QDateEdit()
            to_date.setDate(QDate.currentDate())
            to_date.setCalendarPopup(True)
            filter_layout.addWidget(to_date)

            filter_layout.addWidget(QLabel("العميل:"))
            customer_filter = QComboBox()
            customer_filter.addItem("جميع العملاء")
            # إضافة العملاء من البيانات الحالية
            customers = set()
            for row in range(self.sales_table.rowCount()):
                customer_item = self.sales_table.item(row, 1)
                if customer_item:
                    customers.add(customer_item.text())
            for customer in sorted(customers):
                customer_filter.addItem(customer)
            filter_layout.addWidget(customer_filter)

            filter_button = QPushButton("🔍 تطبيق الفلتر")
            filter_layout.addWidget(filter_button)

            layout.addLayout(filter_layout)

            # جدول التاريخ
            history_table = QTableWidget()
            history_table.setColumnCount(8)
            history_table.setHorizontalHeaderLabels([
                "التاريخ", "رقم الفاتورة", "العميل", "المنتج",
                "الكمية", "سعر الوحدة", "الإجمالي", "الحالة"
            ])

            # إعداد التحديد للسطر كاملاً
            history_table.setSelectionBehavior(QAbstractItemView.SelectRows)
            history_table.setSelectionMode(QAbstractItemView.SingleSelection)

            # إخفاء شريط التمرير المرئي مع الحفاظ على التمرير بالماوس
            history_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

            # ضبط إعدادات التمرير للتحكم الدقيق
            try:
                scrollbar = history_table.verticalScrollBar()
                if scrollbar:
                    scrollbar.setSingleStep(50)
                    scrollbar.setPageStep(200)
            except Exception:
                pass

            # إضافة معالج التمرير المخصص
            def history_wheelEvent(event):
                try:
                    delta = event.angleDelta().y()
                    if abs(delta) < 120:
                        event.accept()
                        return

                    scrollbar = history_table.verticalScrollBar()
                    if not scrollbar:
                        event.accept()
                        return

                    if delta > 0:
                        scrollbar.triggerAction(QAbstractSlider.SliderSingleStepSub)
                    else:
                        scrollbar.triggerAction(QAbstractSlider.SliderSingleStepAdd)

                    event.accept()
                except Exception:
                    QTableWidget.wheelEvent(history_table, event)

            history_table.wheelEvent = history_wheelEvent

            # تحميل البيانات
            def load_history_data():
                # محاكاة بيانات تاريخية
                history_data = []
                for row in range(self.sales_table.rowCount()):
                    row_data = []
                    for col in [2, 0, 1, 3, 4, 5, 6, 7]:  # إعادة ترتيب الأعمدة
                        item = self.sales_table.item(row, col)
                        row_data.append(item.text() if item else "")
                    history_data.append(row_data)

                # ترتيب حسب التاريخ (الأحدث أولاً)
                history_data.sort(key=lambda x: x[0], reverse=True)

                history_table.setRowCount(len(history_data))
                for row, data in enumerate(history_data):
                    for col, value in enumerate(data):
                        item = QTableWidgetItem(str(value))
                        item.setFont(QFont("Arial", 10, QFont.Bold))

                        # تلوين حسب الحالة
                        if col == 7:  # عمود الحالة
                            if value == "مكتمل":
                                item.setBackground(QColor("#d1fae5"))
                            elif value == "معلق":
                                item.setBackground(QColor("#fef3c7"))
                            elif value == "ملغي":
                                item.setBackground(QColor("#fee2e2"))

                        history_table.setItem(row, col, item)

                # تعديل عرض الأعمدة
                header = history_table.horizontalHeader()
                header.setStretchLastSection(True)
                for i in range(history_table.columnCount()):
                    header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

            # تحميل البيانات الأولية
            load_history_data()

            # ربط زر الفلتر
            filter_button.clicked.connect(load_history_data)

            layout.addWidget(history_table)

            # إحصائيات سريعة
            stats_layout = QHBoxLayout()

            total_sales = history_table.rowCount()
            total_amount = 0
            for row in range(history_table.rowCount()):
                amount_item = history_table.item(row, 6)
                if amount_item:
                    try:
                        amount = float(amount_item.text().replace(',', '').replace('جنيه', '').strip())
                        total_amount += amount
                    except:
                        pass

            stats_label = QLabel(f"📊 إجمالي المبيعات: {total_sales} | 💰 إجمالي المبلغ: {int(total_amount):,} جنيه")
            stats_label.setStyleSheet("font-weight: bold; font-size: 12px; padding: 10px; background-color: #f0f9ff; border-radius: 5px;")
            stats_layout.addWidget(stats_label)

            layout.addLayout(stats_layout)

            # أزرار الإجراءات
            buttons_layout = QHBoxLayout()

            export_history_btn = QPushButton("📤 تصدير التاريخ")
            export_history_btn.clicked.connect(lambda: self.export_sales_history(history_table))
            buttons_layout.addWidget(export_history_btn)

            close_btn = QPushButton("❌ إغلاق")
            close_btn.clicked.connect(dialog.close)
            buttons_layout.addWidget(close_btn)

            layout.addLayout(buttons_layout)
            dialog.setLayout(layout)
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في عرض تاريخ المبيعات: {str(e)}")

    def export_sales_history(self, history_table):
        """تصدير تاريخ المبيعات"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtCore import QDate
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تاريخ المبيعات", "تاريخ_المبيعات.csv", "CSV Files (*.csv)"
            )

            if file_path:
                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة العناوين
                    headers = []
                    for col in range(history_table.columnCount()):
                        headers.append(history_table.horizontalHeaderItem(col).text())
                    writer.writerow(headers)

                    # كتابة البيانات
                    for row in range(history_table.rowCount()):
                        row_data = []
                        for col in range(history_table.columnCount()):
                            item = history_table.item(row, col)
                            row_data.append(item.text() if item else "")
                        writer.writerow(row_data)

                QMessageBox.information(self, "تم", f"تم تصدير تاريخ المبيعات بنجاح إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تصدير التاريخ: {str(e)}")

    def view_customer_info(self):
        """عرض معلومات العميل"""
        selected_row = self.sales_table.currentRow()
        if selected_row < 0:
            QMessageBox.warning(self, "تحذير", "الرجاء اختيار مبيعة من القائمة")
            return

        customer = self.sales_table.item(selected_row, 2).text() if self.sales_table.item(selected_row, 2) else ""
        QMessageBox.information(self, "معلومات العميل", f"معلومات العميل: {customer}")

    def create_custom_status_filter(self):
        """إنشاء قائمة تصفية مخصصة ومطورة للحالات مطابقة للفواتير"""
        # إنشاء إطار للقائمة المخصصة
        self.status_filter_frame = QFrame()
        self.status_filter_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 6px 15px;
                min-width: 500px;
                max-width: 500px;
                min-height: 33px;
                max-height: 37px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                cursor: pointer;
            }
            QFrame:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.9);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
        """)

        # إنشاء تخطيط أفقي للإطار
        filter_layout = QHBoxLayout(self.status_filter_frame)
        filter_layout.setContentsMargins(5, 0, 5, 0)
        filter_layout.setSpacing(8)

        # سهم يسار
        self.status_left_arrow = QPushButton("▼")
        self.status_left_arrow.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)

        # النص الحالي
        self.current_status_label = QLabel("جميع الحالات")
        self.current_status_label.setAlignment(Qt.AlignCenter)
        self.current_status_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                font-size: 16px;
                font-weight: 900;
                background: transparent;
                border: none;
                padding: 0px 12px;
                text-align: center;
                max-width: 435px;
                min-width: 435px;
                text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
                cursor: pointer;
            }
        """)

        # زر القائمة (سهم يمين)
        self.status_menu_button = QPushButton("▼")
        self.status_menu_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)

        # إضافة العناصر للتخطيط
        filter_layout.addWidget(self.status_left_arrow, 0)
        filter_layout.addWidget(self.current_status_label, 1)
        filter_layout.addWidget(self.status_menu_button, 0)

        # إنشاء القائمة المنسدلة للحالات
        self.status_menu = QMenu(self)
        self.status_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px;
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                min-width: 515px;
                max-width: 515px;
            }
            QMenu::item {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px;
                margin: 2px 5px;
                min-height: 32px;
                max-height: 32px;
                max-width: 495px;
                min-width: 495px;
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                text-align: center;
                font-smooth: never;
                -webkit-font-smoothing: none;
                -moz-osx-font-smoothing: auto;
                text-rendering: optimizeSpeed;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.4),
                    stop:0.2 rgba(139, 92, 246, 0.3),
                    stop:0.4 rgba(124, 58, 237, 0.25),
                    stop:0.6 rgba(139, 92, 246, 0.3),
                    stop:0.8 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(59, 130, 246, 0.35));
                border: 3px solid rgba(96, 165, 250, 0.7);
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                font-family: 'Courier New', 'Consolas', monospace;
                font-smooth: never;
                -webkit-font-smoothing: none;
                -moz-osx-font-smoothing: auto;
                text-rendering: optimizeSpeed;
                box-shadow:
                    0 4px 12px rgba(96, 165, 250, 0.3),
                    0 0 15px rgba(96, 165, 250, 0.2),
                    inset 0 1px 2px rgba(255, 255, 255, 0.5);
                transform: scale(1.02);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.9);
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                font-family: 'Courier New', 'Consolas', monospace;
                font-smooth: never;
                -webkit-font-smoothing: none;
                -moz-osx-font-smoothing: auto;
                text-rendering: optimizeSpeed;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                transform: translateY(-1px);
            }
        """)

        # إضافة خيارات التصفية للحالات
        status_options = [
            ("جميع الحالات", "all"),
            ("مدفوعة", "مدفوعة"),
            ("معلقة", "معلقة"),
            ("ملغية", "ملغية")
        ]

        for text, value in status_options:
            # إنشاء عنصر مع توسيط النص المثالي
            centered_text = f"{text:^35}"  # توسيط النص في مساحة 35 حرف للعرض الجديد
            action = QAction(centered_text, self)
            action.setData(value)
            action.triggered.connect(lambda checked, v=value, t=text: self.set_status_filter(v, t))
            self.status_menu.addAction(action)

        # ربط الأزرار بالقائمة
        self.status_menu_button.clicked.connect(self.show_status_menu)
        self.status_left_arrow.clicked.connect(self.show_status_menu)

        # إضافة ميزة الضغط على أي مكان في الإطار
        self.status_filter_frame.mousePressEvent = self.status_frame_mouse_press_event
        self.current_status_label.mousePressEvent = self.status_frame_mouse_press_event

        # تعيين القيم الافتراضية
        self.current_status_value = "all"

    def show_status_menu(self):
        """عرض قائمة تصفية الحالات"""
        try:
            button = self.sender()
            if button:
                # إذا تم استدعاؤها من زر
                self.status_menu.exec_(button.mapToGlobal(button.rect().bottomLeft()))
            else:
                # إذا تم استدعاؤها من mousePressEvent
                self.status_menu.exec_(self.status_filter_frame.mapToGlobal(self.status_filter_frame.rect().bottomLeft()))
        except Exception as e:
            print(f"خطأ في عرض قائمة الحالات: {str(e)}")
            # عرض القائمة في موقع افتراضي
            self.status_menu.exec_(self.status_filter_frame.mapToGlobal(self.status_filter_frame.rect().bottomLeft()))

    def set_status_filter(self, value, text):
        """تعيين تصفية الحالة"""
        self.current_status_value = value
        self.current_status_label.setText(text)
        self.filter_sales()

    def status_frame_mouse_press_event(self, event):
        """التعامل مع الضغط على إطار تصفية الحالة"""
        self.show_status_menu()
